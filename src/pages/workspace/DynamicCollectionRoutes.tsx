import React from "react";

import range from "lodash/range";
import { Route, Routes, useOutletContext } from "react-router-dom";

import { CollectionFormPage } from "@pages/collection/forms/CollectionFormPage";
import { CollectionFormProvider } from "@pages/collection/forms/CollectionFormProvider";
import { CollectionHomeProvider } from "@pages/collection/home/<USER>";
import { Home } from "@pages/collection/home/<USER>";
import { ErrorElement } from "@pages/errors/ErrorElement";

import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace";

export const DynamicCollectionRoutes = () => {
  const { workspace, document } = useOutletContext<{
    workspace: Workspace;
    document: WorkspaceDocument;
  }>();

  if (!workspace?.key || !document?.foundations.order) {
    return [];
  }

  return (
    <Routes>
      {document?.foundations.order.map((_, foundationLevel: number) => {
        const levelPaths = range(1, foundationLevel + 1)
          .map(i => `/:level${i}Key`)
          .join("");
        const path = `/browse${levelPaths}`;
        const formPath = `${path}/form/:formIdentifier`;
        return (
          <React.Fragment key={`foundationLevel-${foundationLevel}`}>
            <Route
              key={path}
              path={path}
              element={
                <CollectionHomeProvider level={foundationLevel}>
                  <Home />
                </CollectionHomeProvider>
              }
            />
            <Route
              element={
                <CollectionHomeProvider level={foundationLevel}>
                  <CollectionFormProvider>
                    <CollectionFormPage />
                  </CollectionFormProvider>
                </CollectionHomeProvider>
              }
              key={formPath}
              path={formPath}
            />
            <Route
              element={<ErrorElement />}
              key={`${path}/*`}
              path={`${path}/*`}
            />
          </React.Fragment>
        );
      })}
    </Routes>
  );
};
