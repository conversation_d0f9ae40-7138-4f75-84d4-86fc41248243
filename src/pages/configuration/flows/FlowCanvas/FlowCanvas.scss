.flow-canvas {
  --xy-edge-stroke-width-default: 2;
  --xy-edge-stroke-default: var(
    --components-whiteboard-line-color-background,
    #dae3ed
  );

  & .flow-canvas__mini-map {
    box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.1);
    border: 1px solid
      var(--components-whiteboard-line-color-background, #dae3ed);
    margin-bottom: 80px;
    border-radius: var(--components-whiteboard-toolbar-mode-border-radius, 8px);
    overflow: hidden;
  }

  & .react-flow__edgelabel-renderer {
    & .flow-edge-label {
      color: var(--color-text-primary);
      background: #dde3eb;
    }
  }
}

.react-flow__panel {
  transition: 0.2s;
  margin: var(--components-page-body-template-padding-vertical, 16px)
    var(--components-page-body-template-padding-horizontal, 24px);
  & .resizable-container {
    max-height: 80vh;
  }
  &.center {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flow-step-last-edge {
  width: 1px !important;
  height: 40px !important;
}

.react-flow__node-flowStep {
  &:has(.iterator-flow-step-wrapper) {
    z-index: 1 !important;
  }
  &:has(.whiteboard-add-button--open) {
    z-index: 100 !important;
  }
}

.react-flow__node-addStepNode {
  z-index: 100 !important;
}
