import React, { use<PERSON><PERSON><PERSON>, use<PERSON>emo, useState } from "react";

import {
  Button,
  ButtonGroup,
  ButtonVariant,
  Label,
  Modal,
  ModalDialog,
  MultiSelectValue,
  Overlay,
  SelectValue,
  Stack,
  UserMultiSelect
} from "@oneteam/onetheme";

import {
  useAddWorkspaceUser,
  useUsersInTenantSearch,
  useWorkspaceUsersSearch
} from "@helpers/userHelper";

import { useDictionary } from "@src/hooks/useDictionary";
import { Page } from "@src/types/Page";
import {
  NewWorkspaceUser,
  WorkspaceAccessLevel,
  WorkspaceUserSearch,
  WorkspaceUserStatus
} from "@src/types/WorkspaceUser";
import { Workspace } from "@src/types/workspace";

import { SelectWorkspacePermissionLevel } from "../SelectWorkspacePermissionLevel";
import "./AddWorkspaceUsersModal.scss";

export const AddWorkspaceUsersModal = ({
  workspace,
  closeModal
}: {
  workspace: Workspace;
  closeModal: () => void;
}) => {
  const d = useDictionary();
  const [selectedUserIds, setSelectedUserIds] = useState<MultiSelectValue[]>(
    []
  );

  const { addWorkspaceUsers } = useAddWorkspaceUser({
    workspace
  });

  const [levels, setLevels] = React.useState<WorkspaceAccessLevel[]>([
    WorkspaceAccessLevel.COLLECTION
  ]);

  // Get list of all current users in the tenant
  const { data: users } = useUsersInTenantSearch();
  const { data: existingWorkspaceUsers } = useWorkspaceUsersSearch(
    workspace.id
  ) as { data: Page<WorkspaceUserSearch> };

  const formatUsers = useMemo(() => {
    if (!users?.items) {
      return [];
    }

    const existingWorkspaceUserIds =
      existingWorkspaceUsers?.items?.map(user => user.user.id) || [];

    return users.items
      .filter(user => !existingWorkspaceUserIds.includes(user.id))
      .map(user => ({
        id: user.id,
        firstName: user.properties?.firstName ?? "",
        lastName: user.properties?.lastName ?? "",
        description: user.email
      }));
  }, [users?.items, existingWorkspaceUsers?.items]);

  const handleAddUsers = useCallback(
    (users: MultiSelectValue[]) => {
      const newUsers: NewWorkspaceUser[] = users.map(id => ({
        workspaceId: workspace.id,
        userId: Number(id),
        accessLevel: levels,
        status: WorkspaceUserStatus.ACTIVE
      }));
      addWorkspaceUsers(newUsers);
      closeModal();
    },
    [workspace.id, levels, addWorkspaceUsers, closeModal]
  );

  return (
    <ModalDialog isOpen>
      <Overlay isOpen />
      <Modal
        className="add-workspace-users-modal"
        isOpen
        heading={d("ui.settings.permissions.addUsers.title")}
        onOpenChange={closeModal}
        footer={
          <ButtonGroup alignment="right">
            <Button
              variant={ButtonVariant.TEXT}
              label={d("ui.common.cancel")}
              onClick={closeModal}
            />
            <Button
              variant={ButtonVariant.PRIMARY}
              label={d("ui.settings.permissions.addUsers.title")}
              onClick={() => {
                handleAddUsers(selectedUserIds);
              }}
              disabled={!levels.length}
            />
          </ButtonGroup>
        }
      >
        <Stack gap="200">
          <UserMultiSelect
            label={d("ui.settings.permissions.addUsers.usersSelect.label")}
            required
            placeholder={d(
              "ui.settings.permissions.addUsers.usersSelect.placeholder"
            )}
            users={formatUsers}
            onChange={(value?: SelectValue | undefined) => {
              const multiSelectValue = value as MultiSelectValue[] | undefined;
              if (!multiSelectValue) {
                setSelectedUserIds([]);
                return;
              }
              setSelectedUserIds(multiSelectValue);
            }}
          />
          <Stack gap="100">
            <Label
              label={d(
                "ui.settings.permissions.addUsers.permissionLevelSelect.label"
              )}
              required
              description={d(
                "ui.settings.permissions.addUsers.permissionLevelSelect.description"
              )}
            />
            <SelectWorkspacePermissionLevel
              value={levels}
              onChange={setLevels}
              isInitialAdd
            />
          </Stack>
        </Stack>
      </Modal>
    </ModalDialog>
  );
};
