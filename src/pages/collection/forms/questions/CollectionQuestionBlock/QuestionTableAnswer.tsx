import React, {
  RefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import {
  Box,
  Button,
  FileUpload,
  IconButton,
  IconFillStyle,
  Inline,
  Stack,
  ToastNotificationVariant,
  getClassNames,
  returnStringIfTrue
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import {
  addItem,
  mapOverResource,
  removeItem
} from "@helpers/OrderedMapNoState.ts";
import { getByPath } from "@helpers/automergeDocumentHelper.ts";
import { eventWithoutPropagationDefault } from "@helpers/eventWithoutPropagation.ts";
import { downloadTableData, uploadTableDataForPrefill } from "@helpers/files";
import { updateAnswer } from "@helpers/forms/answerHelper";

import {
  MAX_FILE_SIZE_MB,
  fileFormatsToMimeType
} from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Files/FilesHelper";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";
import { QuestionAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionAnswer";
import { QuestionTableHelper } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionTableHelper";
import { PushToastNotification } from "@pages/workspace/WorkspaceLayout";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import {
  TableCellLocation,
  TableColumnLocation,
  TableRowLocation
} from "@src/types/AnnotationLocation";
import { Question } from "@src/types/Question.ts";
import { TableQuestionProperties } from "@src/types/QuestionProperties.ts";
import {
  AnswerDocChange,
  FormAnswer,
  QuestionAnswers,
  TableAnswer,
  TableAnswerRow
} from "@src/types/collection/CollectionForm.ts";
import { Resource } from "@src/types/documentTypes.ts";

import { HighlightAnnotationLocation } from "../../HighlightAnnotationLocation/HighlightAnnotationLocation";
import { LabelWithHighlight } from "../../LabelWithHighlight";
import { CollectionQuestionBlockAlert } from "./CollectionQuestionBlockAlert/CollectionQuestionBlockAlert";
import { CollectionQuestionBlockComment } from "./CollectionQuestionBlockComment/CollectionQuestionBlockComment";

// TODO: move to OTAIFormFieldTable
export const QuestionTableAnswer = ({
  d,
  question,
  answer,
  answerAccessor,
  disableAddRow = false,
  showPreviewAnswerRow = false,
  disabled
}: {
  d: Dictionary;
  question: Question<TableQuestionProperties>;
  answer?: FormAnswer<TableAnswer>;
  answerAccessor: string;
  disableAddRow?: boolean;
  showPreviewAnswerRow?: boolean;
  disabled?: boolean;
}) => {
  const { pushToastNotification } = useOutletContext<{
    pushToastNotification: PushToastNotification;
  }>();
  const { docChange, collectionFormId, summaryModal, documentId } =
    useCollectionFormContext();
  const restrictedFileTypes = fileFormatsToMimeType(["spreadsheet"]);
  const [transientError, setTransientError] = useState<string | undefined>();
  const validateFileSize = useCallback(
    (file: File) => {
      const maxFileSizeBytes = MAX_FILE_SIZE_MB * 1024 * 1024;
      if (file.size > maxFileSizeBytes) {
        setTransientError(
          d(
            "errors.configurationForm.question.files.maxFileSize.fileTooLarge",
            { maxFileSize: MAX_FILE_SIZE_MB }
          )
        );
        return false;
      }
      return true;
    },
    [d]
  );

  const questionDisabled = useMemo(
    () => question.properties?.disabled || disabled,
    [question.properties?.disabled, disabled]
  );

  const downloadDataFile = useCallback(async () => {
    if (!collectionFormId) {
      return;
    }

    pushToastNotification(
      d("ui.forms.question.table.notifications.download.started.heading"),
      d("ui.forms.question.table.notifications.download.started.description"),
      ToastNotificationVariant.INFO
    );

    await downloadTableData(
      collectionFormId,
      question.id,
      question.identifier
    ).catch(() => {
      pushToastNotification(
        d("ui.forms.question.table.notifications.download.failed.heading"),
        d("ui.forms.question.table.notifications.download.failed.description"),
        ToastNotificationVariant.DANGER
      );
    });
  }, [
    pushToastNotification,
    d,
    collectionFormId,
    question.id,
    question.identifier
  ]);

  const onChange = useCallback(
    (_: string | string[], fileList: FileList | null) => {
      setTransientError(undefined);
      if (!collectionFormId) {
        return;
      }
      if (!fileList) {
        alert(d("errors.configurationForm.question.files.noFilesSelected"));
        return;
      }

      for (const file of fileList) {
        if (!validateFileSize(file)) {
          return;
        }
      }

      // NB: We expect at most 1 file
      const newFiles = Array.from(fileList);
      newFiles.forEach(file => {
        pushToastNotification(
          d("ui.forms.question.table.notifications.upload.started.heading"),
          d("ui.forms.question.table.notifications.upload.started.description"),
          ToastNotificationVariant.INFO
        );

        uploadTableDataForPrefill(collectionFormId, question.id, file)
          .then(({ status }) => {
            if (status === 500) {
              throw new Error("Failed to process file");
            }

            pushToastNotification(
              d("ui.forms.question.table.notifications.upload.success.heading"),
              d(
                "ui.forms.question.table.notifications.upload.success.description"
              ),
              ToastNotificationVariant.SUCCESS
            );
          })
          .catch(() => {
            pushToastNotification(
              d("ui.forms.question.table.notifications.upload.failed.heading"),
              d(
                "ui.forms.question.table.notifications.upload.failed.description"
              ),
              ToastNotificationVariant.DANGER
            );
          });
      });
      return true;
    },
    [collectionFormId, validateFileSize, question.id, d, pushToastNotification]
  );

  const questionTableHelper = useMemo(() => {
    return new QuestionTableHelper(question);
  }, [question]);

  const createEmptyAnswerRow = useCallback(() => {
    return addItem(questionTableHelper.createNewRow(), {
      entities: {},
      order: []
    });
  }, [questionTableHelper]);

  useEffect(() => {
    // if there are no rows on the table answer yet, create the first one
    if (docChange) {
      docChange(d => {
        const v = d.answers[question.id]?.value as TableAnswer;
        if (!v?.order?.length && question.properties) {
          const resource = createEmptyAnswerRow();
          d.answers[question.id] = {
            questionId: question.id,
            value: resource,
            type: question.type
          };
        }
      });
    }
  }, [docChange, createEmptyAnswerRow, question]);

  const rowAnswer = useMemo(() => {
    if (showPreviewAnswerRow) {
      return createEmptyAnswerRow();
    }
    return answer?.value;
  }, [showPreviewAnswerRow, createEmptyAnswerRow, answer?.value]);

  // accessor to the row in question - should look like `answers.VEOTahOeLj.value.entities.yD6Dq_LaVDhyD12CXlGJe.columns`
  const removeTableRow = useCallback(
    (accessor: string, docChange?: AnswerDocChange) => {
      if (docChange) {
        docChange(d => {
          const rowId = questionTableHelper.rowId(accessor);
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            d,
            answerAccessor.split(".")
          );
          const updatedTable = removeItem(
            rowId,
            JSON.parse(JSON.stringify(questionAnswers))
          );
          updateAnswer(
            question,
            updatedTable as TableAnswer,
            answerAccessor,
            documentId
          );
        });
      }
    },
    [answerAccessor, questionTableHelper, question, documentId]
  );

  const addTableRow = useCallback(
    (
      question: Question<TableQuestionProperties>,
      docChange?: AnswerDocChange
    ) => {
      if (docChange) {
        docChange(d => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            d,
            answerAccessor.split(".")
          );
          if (questionAnswers && question.properties) {
            // update the document with a new row
            addItem(questionTableHelper.createNewRow(), questionAnswers);
          } else {
            console.error("Path not found", answerAccessor);
          }
        });
      }
    },
    [answerAccessor, questionTableHelper]
  );

  const displayColumnQuestions = useMemo(() => {
    return question.properties?.columns?.filter(c => !c.properties?.hidden);
  }, [question.properties?.columns]);
  const parentRef = useRef<HTMLElement>();

  return (
    <Stack className="table-question__container">
      <Box className="table-question" position="relative" overflow="auto">
        <table className="table-question__table" style={{ minWidth: "100%" }}>
          <thead className="table-question__table__header">
            <tr
              key={`table-header-${question.id}`}
              className={getClassNames(["table-question__table__header__row"])}
            >
              <th
                key="table-header-question-counter"
                className="table-question__table__header__cell table-question__table__data__cell--counter"
                ref={parentRef as RefObject<HTMLTableCellElement>}
              />
              {displayColumnQuestions?.map(columnQuestion => (
                <th
                  key={`table-header-question-counter--${columnQuestion.id}`}
                  className={getClassNames([
                    "table-question__table__header__cell",
                    `table-question__table__data__cell--${columnQuestion.identifier}`,
                    returnStringIfTrue(
                      (summaryModal.location?.variant === "question" ||
                        summaryModal.location?.variant === "answer") &&
                        summaryModal.location?.questionId === question.id &&
                        (summaryModal.location as TableColumnLocation)
                          ?.columnId === columnQuestion.id &&
                        !(summaryModal.location as TableCellLocation).rowId,
                      `table-question__table__data__cell--selected`
                    )
                  ])}
                >
                  <Inline gap="100" alignment="left" width="100">
                    <HighlightAnnotationLocation
                      location={{
                        variant: "question",
                        questionId: question.id,
                        columnId: columnQuestion.id
                      }}
                    >
                      <LabelWithHighlight
                        className="table-question__table__header__cell__label"
                        label={columnQuestion?.text}
                        required={columnQuestion?.properties?.required}
                        description={columnQuestion?.description}
                      />
                    </HighlightAnnotationLocation>
                    {/* Column alert */}
                    <CollectionQuestionBlockAlert
                      location={{
                        variant: "question",
                        questionId: question.id,
                        columnId: columnQuestion.id
                      }}
                      stylingVariant="cellIndicator"
                      floatingAnnotationPosition="bottom-left"
                    />
                    {/* Column comment */}
                    <CollectionQuestionBlockComment
                      location={{
                        variant: "question",
                        questionId: question.id,
                        columnId: columnQuestion.id
                      }}
                      stylingVariant="cellIndicator"
                      floatingAnnotationPosition="bottom-left"
                    />
                  </Inline>
                </th>
              ))}
              <th
                key="table-header-question-actions"
                className="table-question__table__header__cell table-question__table__data__cell--actions"
              />
            </tr>
          </thead>
          <tbody className="table-question__table__data">
            {/* Map answers */}
            {rowAnswer &&
              typeof rowAnswer === "object" &&
              mapOverResource(rowAnswer, (row, rowIndex) => {
                return (
                  <TableAnswerRowComponent
                    key={`table-row-${row.id}`}
                    question={question}
                    row={row.columns}
                    rowId={row.id}
                    rowIndex={rowIndex}
                    displayColumnQuestions={
                      displayColumnQuestions as Question[]
                    }
                    answerAccessor={`${answerAccessor}.entities.${row.id}.columns`}
                    removeTableRow={removeTableRow}
                    disabled={questionDisabled}
                  />
                );
              })}
          </tbody>
        </table>
      </Box>
      <Inline spaceBetween alignment="left">
        <Box width={"fit"} style={{ position: "sticky", left: 0 }}>
          {!disableAddRow && !questionDisabled && (
            <Button
              variant="secondary"
              leftIcon={{ name: "add", fillStyle: IconFillStyle.FILLED }}
              onClick={() => addTableRow(question, docChange)}
              label={d("ui.forms.question.table.addRow")}
            />
          )}
        </Box>

        <Inline gap="150">
          {!questionDisabled && (
            <FileUpload
              multiple={false}
              acceptableTypes={restrictedFileTypes}
              error={transientError}
              onChange={onChange}
              trigger={({ onClick }) => (
                <Button
                  variant="text"
                  leftIcon={{ name: "upload", fillStyle: IconFillStyle.FILLED }}
                  onClick={onClick}
                  label={d("ui.forms.question.table.upload")}
                />
              )}
            />
          )}
          <Button
            variant="text"
            leftIcon={{ name: "download", fillStyle: IconFillStyle.FILLED }}
            onClick={downloadDataFile}
            label={d("ui.forms.question.table.download")}
          />
        </Inline>
      </Inline>
    </Stack>
  );
};

const TableAnswerRowComponent = ({
  question,
  row,
  rowId,
  rowIndex,
  displayColumnQuestions,
  answerAccessor,
  removeTableRow,
  disabled
}: {
  question: Question<TableQuestionProperties>;
  row: QuestionAnswers;
  rowId: string;
  rowIndex: number;
  displayColumnQuestions?: Question[];
  answerAccessor: string;
  removeTableRow: (accessor: string, docChange?: AnswerDocChange) => void;
  disabled?: boolean;
}) => {
  const { docChange, summaryModal } = useCollectionFormContext();
  const parentRef = useRef<HTMLElement>();
  const rightParentRef = useRef<HTMLElement>();

  return (
    <tr
      key={`table-row-${question.id}`}
      className={getClassNames([
        "table-question__table__data__row",
        returnStringIfTrue(
          summaryModal.location?.variant === "question" &&
            summaryModal.location?.questionId === question.id &&
            (summaryModal.location as TableRowLocation)?.rowId === rowId &&
            !(summaryModal.location as TableCellLocation).columnId,
          `table-question__table__data__row--selected`
        )
      ])}
      ref={parentRef as RefObject<HTMLTableRowElement>}
    >
      <td
        key={`table-row-${question.id}-counter`}
        className={getClassNames(["table-question__table__data__counter"])}
      >
        <Inline gap="050" alignment="center" width="100">
          <HighlightAnnotationLocation
            location={{
              variant: "question",
              questionId: question.id,
              rowId
            }}
          >
            <LabelWithHighlight label={String(rowIndex + 1)} />
          </HighlightAnnotationLocation>
          {/* Row alert */}
          <CollectionQuestionBlockAlert
            location={{
              variant: "question",
              questionId: question.id,
              rowId
            }}
            stylingVariant="cellIndicator"
            floatingAnnotationPosition="bottom-right"
          />
        </Inline>
        <CollectionQuestionBlockComment
          location={{
            variant: "question",
            questionId: question.id,
            rowId
          }}
          stylingVariant="cellIndicator"
          floatingAnnotationPosition="bottom-right"
        />
      </td>
      {displayColumnQuestions?.map(columnQuestion => {
        return (
          <td
            key={`table-row-${question.id}-${rowIndex}-${columnQuestion.id}`}
            className={getClassNames([
              "table-question__table__data__cell",
              `table-question__table__data__cell--question-${columnQuestion.identifier}`,
              returnStringIfTrue(
                summaryModal.location?.variant === "question" &&
                  summaryModal.location?.questionId === question.id &&
                  (summaryModal.location as TableCellLocation).columnId ===
                    columnQuestion.id &&
                  ((summaryModal.location as TableCellLocation)?.rowId ===
                    rowId ||
                    // Column selected
                    !(summaryModal.location as TableCellLocation).rowId),
                `table-question__table__data__cell--selected`
              )
            ])}
          >
            <Inline
              className="question-answer"
              height="100"
              alignment="left"
              gap="050"
            >
              <Box
                className="question-answer__field"
                width="100"
                height="100"
                contentsHeight="100"
                contentsWidth="100"
              >
                <QuestionAnswer
                  question={columnQuestion}
                  disabled={
                    disabled ||
                    columnQuestion.properties?.disabled ||
                    question.properties?.disabled
                  }
                  answer={row[columnQuestion.id]?.value as FormAnswer["value"]}
                  answerAccessor={`${answerAccessor}.${columnQuestion.id}.value`}
                  location={{
                    variant: "answer",
                    questionId: question.id,
                    rowId,
                    columnId: columnQuestion.id
                  }}
                />
              </Box>
              {/* Cell alert */}
              <CollectionQuestionBlockAlert
                location={{
                  variant: "question",
                  questionId: question.id,
                  rowId,
                  columnId: columnQuestion.id
                }}
                stylingVariant="cellIndicator"
              />
              {/* Cell comment */}
              <CollectionQuestionBlockComment
                location={{
                  variant: "question",
                  questionId: question.id,
                  rowId,
                  columnId: columnQuestion.id
                }}
                stylingVariant="cellIndicator"
              />
            </Inline>
          </td>
        );
      })}
      <td
        className="table-question__table__data__cell table-question__table__data__cell--actions"
        ref={rightParentRef as RefObject<HTMLTableCellElement>}
      >
        {!disabled && (
          <IconButton
            className="delete-row-icon"
            name="delete"
            color="text-secondary"
            fillStyle={IconFillStyle.OUTLINED}
            onClick={eventWithoutPropagationDefault(() =>
              removeTableRow(answerAccessor, docChange)
            )}
            disabled={disabled}
            skipFocus
          />
        )}
      </td>
    </tr>
  );
};

QuestionTableAnswer.displayName = "QuestionTableAnswer";
TableAnswerRowComponent.displayName = "TableAnswerRowComponent";
