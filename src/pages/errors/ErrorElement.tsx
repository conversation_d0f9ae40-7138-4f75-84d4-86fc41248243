import React from "react";

import {
  <PERSON><PERSON>,
  ButtonGroup,
  ButtonVariant,
  ColorText,
  Icon,
  IconFillStyle,
  Stack,
  Text
} from "@oneteam/onetheme";
import { useNavigate, useRouteError } from "react-router-dom";

import { useDictionary } from "@src/hooks/useDictionary";

import "./ErrorElement.scss";

export const ErrorElement = () => {
  const error = useRouteError();
  const navigate = useNavigate();

  const d = useDictionary();

  console.error(error);
  return (
    <Stack
      className="error-element"
      gap="200"
      width="100"
      height="100"
      alignment="center"
    >
      <Icon
        className="error-element__icon"
        name="sentiment_dissatisfied"
        color={ColorText.DISABLED}
        fillStyle={IconFillStyle.OUTLINED}
        sizeOverride="80px"
      />
      <Stack
        alignment="center"
        gap="100"
        style={{
          fontSize: "var(--font-size-heading-m, 24px)"
        }}
      >
        <Text
          className="error-element__title"
          maxLines={2}
          size="inherit"
          weight="medium"
          alignment="center"
        >
          {d("ui.errorPage.notFound.title")}
        </Text>
        <Text
          className="error-element__info"
          alignment="center"
          size="m"
          style={{
            maxWidth: "400px"
          }}
        >
          {d("ui.errorPage.notFound.description")}
        </Text>
      </Stack>
      <ButtonGroup
        padding="100"
        className="error-element__buttons"
        wrap
        alignment="center"
      >
        <Button
          label={d("ui.errorPage.notFound.refresh")}
          variant={ButtonVariant.SECONDARY}
          leftIcon={{ name: "refresh" }}
          onClick={() => navigate(0)}
        />
        <Button
          label={d("ui.errorPage.notFound.home")}
          leftIcon={{ name: "home" }}
          onClick={() => {
            navigate("/");
          }}
        />
      </ButtonGroup>
    </Stack>
  );
};
