import React from "react";

import { Form, Stack } from "@oneteam/onetheme";
import type { <PERSON>a, StoryObj } from "@storybook/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { initialize, mswDecorator, mswLoader } from "msw-storybook-addon";

import { ZodAnswerSchemaBuilder } from "@helpers/forms/ZodAnswerSchemaBuilder/ZodAnswerSchemaBuilder.ts";

import { OTAIForm } from "@components/shared/OTAIForm/OTAIForm.tsx";
import { OTAIFormField } from "@components/shared/OTAIForm/OTAIFormField.tsx";

import { MockOutletContextProvider } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowStepModal/MockOutletContextProvider";

import {
  ANZ_FOUNDATION_ID,
  foundationCollectionMockAPIHandlers,
  foundationCollectionMockAPIHandlersLoading
} from "@src/hooks/formConfiguration/select/mswHandlers.ts";
import { Dictionary } from "@src/hooks/useDictionary";
import { Section } from "@src/types/FormConfiguration";
import { FoundationRelationship } from "@src/types/FoundationConfiguration.ts";
import { Question } from "@src/types/Question";
import { DynamicOptionTags } from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

// Initialize MSW
initialize({
  onUnhandledRequest: "bypass"
});

const contextValue = {
  workspace: {
    id: "1"
  },
  document: sampleWorkspaceDocument()
};

const meta: Meta<typeof OTAIForm> = {
  component: OTAIForm,
  title: "shared/OTAIForm/OTAIFormFields/DynamicSelect",
  decorators: [
    Story => (
      <MockOutletContextProvider context={contextValue}>
        <Story />
      </MockOutletContextProvider>
    ),
    mswDecorator
  ]
};

export default meta;

type Story = StoryObj<typeof OTAIForm>;

const defaultHandlers: Story["args"] = {
  formWrapperProperties: {
    handleCancel: () => {
      console.log("Cancel");
    },
    handleSubmit: data => {
      console.log("Save", data);
    }
  }
};

const defaultRenderer = (args: Story["args"]) => {
  return <OTAIForm {...args} content={args?.content ?? []} />;
};

const queryClientWrappedRenderer = (args: Story["args"]) => {
  const queryClient = new QueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      {defaultRenderer(args)}
    </QueryClientProvider>
  );
};

const standaloneFieldsRender = () => {
  const questions: Question[] = [
    {
      description: "",
      id: "staticSelectQuestionId",
      identifier: "staticSelectQuestionIdentifier",
      properties: {
        allowReuseAcrossForms: false,
        defaultValue: "",
        placeholder: "",
        required: true,
        options: [
          {
            label: "Name 1",
            value: "name1"
          },
          {
            label: "Name 2",
            value: "name2"
          }
        ]
      },
      type: "select",
      text: "Static Select Question"
    },
    {
      description: "",
      id: "staticMultiSelectQuestionId",
      identifier: "staticMultiSelectQuestionIdentifier",
      properties: {
        allowReuseAcrossForms: false,
        defaultValue: ["name1"],
        isMultiSelect: true, // to remove
        placeholder: "",
        required: true,
        options: [
          {
            label: "Name 1",
            value: "name1"
          },
          {
            label: "Name 2",
            value: "name2"
          }
        ]
      },
      type: "select",
      text: "Static Multi Select Question With Default"
    },
    {
      description: "",
      id: "dynamicSelectFormConfig_Id",
      identifier: "dynamicSelectFormConfig_Identifier",
      properties: {
        allowReuseAcrossForms: false,
        defaultValue: "",
        placeholder: "",
        required: false,
        dynamicOptions: {
          tag: DynamicOptionTags.FORM_CONFIGURATION_ID
        }
      },
      type: "select",
      text: "Dynamic Select FormConfig"
    }
  ];
  const d = (value => value) as Dictionary;

  return (
    <Form
      // We do not use OTAIForm here to show that OTAIFormField can be used standalone
      d={d}
      handleSubmit={data => console.log("Submit", data)}
      schema={new ZodAnswerSchemaBuilder(questions, d).generatedSchema}
    >
      <Stack gap="100" contentsWidth="100">
        {questions.map(question => (
          <OTAIFormField key={question.id} question={question} />
        ))}
      </Stack>
    </Form>
  );
};

export const StandaloneFieldsRender: Story = {
  args: {
    // ...getMockFormDataWithPositioning()
  },
  render: standaloneFieldsRender
};

export const FormConfigurationIdWithDefault: Story = {
  args: {
    ...defaultHandlers,
    ...dynamicSelectFormConfigurationIdWithDefault(false)
  },
  render: defaultRenderer
};

export const FormConfigurationIdWithDefaultMulti: Story = {
  args: {
    ...defaultHandlers,
    ...dynamicSelectFormConfigurationIdWithDefault(true)
  },
  render: defaultRenderer
};

export const FormConfigurationKey: Story = {
  args: {
    ...defaultHandlers,
    ...dynamicSelectFormConfigurationKey()
  },
  render: defaultRenderer
};

export const FormConfigurationQuestionId: Story = {
  args: {
    ...defaultHandlers,
    ...dynamicSelectQuestion()
  },
  render: defaultRenderer
};

export const FoundationConfigurationId: Story = {
  args: {
    ...defaultHandlers,
    ...dynamicSelectFoundationConfigurationId()
  },
  render: defaultRenderer
};

export const FormConfigurationSeriesIntervalId: Story = {
  args: {
    ...defaultHandlers,
    ...dynamicSelectIntervalsFromFormConfiguration()
  },
  render: defaultRenderer
};

export const SeriesConfigurationIntervalId: Story = {
  args: {
    ...defaultHandlers,
    ...dynamicSelectIntervalsFromSeriesConfiguration()
  },
  render: defaultRenderer
};

export const SelectQuestionTypesField: Story = {
  args: {
    ...defaultHandlers,
    ...dynamicSelectQuestionType()
  },
  render: defaultRenderer
};

export const FoundationCollectionIdFromLevelSingleSelectWithDefaultValue: Story =
  DynamicSelectFoundationCollectionIdFromLevelWithDefaultValue(false);
export const FoundationCollectionIdFromLevelMultiSelectWithDefaultValue: Story =
  DynamicSelectFoundationCollectionIdFromLevelWithDefaultValue(true);
export const FoundationCollectionIdFromLevelSingleSelectInfiniteLoading: Story =
  DynamicSelectFoundationCollectionIdFromLevelWithDefaultValue(false, true);

function DynamicSelectFoundationCollectionIdFromLevelWithDefaultValue(
  isMultiSelect: boolean,
  infiniteLoading: boolean = false
): Story {
  return {
    args: {
      ...defaultHandlers,
      ...dynamicSelectFoundationIdFromLevelWithDefaultValue(isMultiSelect)
    },
    parameters: {
      msw: {
        handlers: infiniteLoading
          ? foundationCollectionMockAPIHandlersLoading
          : foundationCollectionMockAPIHandlers(150)
      }
    },
    loaders: [mswLoader],
    render: queryClientWrappedRenderer
  };
}

export const FoundationCollectionKeyFromLevel: Story = {
  args: {
    ...defaultHandlers,
    ...dynamicSelectFoundationCollectionKeyFromLevel()
  },
  parameters: {
    msw: {
      handlers: foundationCollectionMockAPIHandlers
    }
  },
  loaders: [mswLoader],
  render: queryClientWrappedRenderer
};

// export const ApiDynamicSelect: Story = {
//   args: {
//     ...defaultHandlers,
//     ...apiDynamicSelect(),
//     document: sampleWorkspaceDocument()},
//   render: defaultRenderer
// }

function sampleWorkspaceDocument() {
  const sampleWorkspaceDocument: WorkspaceDocument = {
    id: 1,
    name: "Workspace",
    key: "WORKSPACE",
    metadata: {
      createdAt: "2021-08-20T06:00:00Z",
      updatedAt: "2021-08-20T06:00:00Z"
    },
    forms: {
      formId1: {
        id: "formId1",
        name: "Form 1",
        key: "formKey1",
        description: "",
        seriesId: "series-1",
        level: 0,
        foundationId: "1",
        content: [
          {
            content: [
              {
                content: [
                  {
                    description: "",
                    id: "F1-Q1",
                    identifier: "Untitled",
                    properties: {
                      required: false
                    },
                    text: "f1 q1 text",
                    type: "text"
                  }
                ],
                id: "F1-S1",
                level: 2,
                name: "General"
              },
              {
                content: [
                  {
                    description: "",
                    id: "F1-Q2",
                    identifier: "Untitled",
                    properties: {
                      required: false
                    },
                    text: "f1 q2 text",
                    type: "text"
                  }
                ],
                id: "F1-S1",
                level: 2,
                name: "General"
              }
            ],
            id: "section-id",
            level: 1,
            name: "Untitled"
          }
        ]
      },
      formId2: {
        id: "formId2",
        name: "Form 2",
        key: "formKey2",
        description: "",
        seriesId: "",
        level: 0,
        foundationId: "1",
        content: [
          {
            content: [
              {
                content: [
                  {
                    description: "",
                    id: "F2-Q1",
                    identifier: "Untitled",
                    properties: {
                      required: false
                    },
                    text: "f2 q1 text",
                    type: "text"
                  }
                ],
                id: "F2-S1",
                level: 2,
                name: "General"
              },
              {
                content: [
                  {
                    description: "",
                    id: "F2-Q2",
                    identifier: "Untitled",
                    properties: {
                      required: false
                    },
                    text: "f2 q2 text",
                    type: "text"
                  }
                ],
                id: "F2-S1",
                level: 2,
                name: "General"
              }
            ],
            id: "section-id",
            level: 1,
            name: "Untitled"
          }
        ]
      }
    },
    foundations: {
      entities: {
        "1": {
          id: "1",
          name: "Client",
          description: "",
          relationship: FoundationRelationship.ONE_TO_MANY
        }
      },
      order: ["1"]
    },
    series: {
      "series-1": {
        id: "series-1",
        name: "Series 1",
        intervals: {
          entities: {
            "s1-interval-1": { id: "s1-interval-1", name: "Interval 1" },
            "s1-interval-2": { id: "s1-interval-2", name: "Interval 2" }
          },
          order: ["s1-interval-1", "s1-interval-2"]
        },
        metadata: { createdAt: "", updatedAt: "" }
      },
      "series-2": {
        id: "series-2",
        name: "Series 2",
        intervals: {
          entities: {
            "s2-interval-1": {
              id: "s2-interval-1",
              name: "Series2 Interval 1"
            },
            "s2-interval-2": { id: "s2-interval-2", name: "Series2 Interval 2" }
          },
          order: ["s2-interval-1", "s2-interval-2"]
        },
        metadata: { createdAt: "", updatedAt: "" }
      }
    },
    labels: {},
    flows: {
      entities: {},
      order: []
    }
  };
  return sampleWorkspaceDocument;
}

// Form configuration id
function dynamicSelectFormConfigurationIdWithDefault(
  isMultiSelect: boolean
): Story["args"] {
  const mockForm: Section["content"] = [
    {
      content: [
        {
          text: "Form",
          type: "select",
          id: "form",
          identifier: "form",
          properties: {
            isMultiSelect: isMultiSelect,
            defaultValue: isMultiSelect ? ["formId1", "formId2"] : "formId1",
            dynamicOptions: {
              tag: DynamicOptionTags.FORM_CONFIGURATION_ID
            }
          }
        }
      ],
      id: "1",
      name: "Dynamic Select Options - Forms with id"
    }
  ];
  return { content: mockForm };
}

// Form configuration key
function dynamicSelectFormConfigurationKey(): Story["args"] {
  const mockForm: Section["content"] = [
    {
      content: [
        {
          text: "Form",
          type: "select",
          id: "form",
          identifier: "form",
          properties: {
            dynamicOptions: {
              tag: DynamicOptionTags.FORM_CONFIGURATION_KEY
            }
          }
        }
      ],
      id: "1",
      name: "Dynamic Select Options - Forms with key"
    }
  ];
  return { content: mockForm };
}

// Questions in a specific form configuration
// e.g. value will be questionId and label will be question text
// { value: "1", label: "What is your name" }
function dynamicSelectQuestion(): Story["args"] {
  const mockForm: Section["content"] = [
    {
      content: [
        {
          text: "Question",
          type: "select",
          id: "question",
          identifier: "question",
          properties: {
            dynamicOptions: {
              tag: DynamicOptionTags.FORM_CONFIGURATION_QUESTION_ID,
              // expects a formConfigurationId to be passed in the body
              // gives back a list of questions for the formConfigurationId
              body: {
                formConfigurationId: "formId1"
              }
            }
          }
        }
      ],
      id: "1",
      name: "Dynamic Select Options - Form Questions"
    }
  ];
  return { content: mockForm };
}

// Foundation level
// e.g. { value: "1", label: "Client" }
function dynamicSelectFoundationConfigurationId(): Story["args"] {
  const mockForm: Section["content"] = [
    {
      content: [
        {
          text: "Foundation level",
          type: "select",
          id: "foundationConfigurationId",
          identifier: "foundationConfigurationId",
          properties: {
            dynamicOptions: {
              tag: DynamicOptionTags.FOUNDATION_CONFIGURATION_ID
            }
          }
        }
      ],
      id: "1",
      name: "Dynamic Select Options - Foundation Configurations"
    }
  ];
  return { content: mockForm };
}

function dynamicSelectIntervalsFromFormConfiguration(): Story["args"] {
  const mockForm: Section["content"] = [
    {
      content: [
        {
          text: "Interval",
          type: "select",
          id: "intervalId",
          identifier: "interval",
          properties: {
            dynamicOptions: {
              tag: DynamicOptionTags.FORM_CONFIGURATION_SERIES_INTERVAL_ID,
              // expects a formConfigurationId to be passed in the body
              // gives back a list of intervals for the formConfigurationId
              body: {
                formConfigurationId: "formId1"
              }
            }
          }
        }
      ],
      id: "1",
      name: "Dynamic Select Options - Intervals"
    }
  ];
  return { content: mockForm };
}

function dynamicSelectIntervalsFromSeriesConfiguration(): Story["args"] {
  const mockForm: Section["content"] = [
    {
      content: [
        {
          text: "Interval",
          type: "select",
          id: "intervalId",
          identifier: "interval",
          properties: {
            dynamicOptions: {
              tag: DynamicOptionTags.SERIES_INTERVAL_ID,
              // expects a seriesConfigurationId to be passed in the body
              // gives back a list of intervals for the seriesConfigurationId
              body: {
                seriesConfigurationId: "series-1"
              }
            }
          }
        }
      ],
      id: "1",
      name: "Dynamic Select Options - Intervals"
    }
  ];
  return { content: mockForm };
}

// Foundation from a specific level (collection)
// e.g. { value: "1", label: "ANZ Bank" }
function dynamicSelectFoundationIdFromLevelWithDefaultValue(
  isMultiSelect: boolean
): Story["args"] {
  const mockForm: Section["content"] = [
    {
      content: [
        {
          text: "Foundation",
          type: "select",
          id: "foundationId",
          identifier: "foundation",
          properties: {
            isMultiSelect: isMultiSelect,
            defaultValue: isMultiSelect
              ? [ANZ_FOUNDATION_ID]
              : ANZ_FOUNDATION_ID, // To test dynamic select behaviour with default value
            dynamicOptions: {
              tag: DynamicOptionTags.FOUNDATION_COLLECTION_ID,
              // expects a foundationConfigurationId to be passed in the body
              // gives back a list of collection foundations for the foundationConfigurationId
              body: {
                parentFoundationConfigurationId: "test-id",
                parentFoundationKey: "test-key"
              }
            }
          }
        }
      ],
      id: "1",
      name: "Dynamic Select Options - Foundation Levels"
    }
  ];
  return { content: mockForm };
}

// Foundation from a specific level (collection)
// e.g. { value: "ANZ", label: "ANZ Bank" }
function dynamicSelectFoundationCollectionKeyFromLevel(): Story["args"] {
  const mockForm: Section["content"] = [
    {
      content: [
        {
          text: "Foundation",
          type: "select",
          id: "foundationKey",
          identifier: "foundation",
          properties: {
            dynamicOptions: {
              tag: DynamicOptionTags.FOUNDATION_COLLECTION_KEY,
              // expects a foundationConfigurationId to be passed in the body
              // gives back a list of collection foundations for the foundationConfigurationId
              body: {
                parentFoundationConfigurationId: "test-id",
                parentFoundationKey: "test-key"
              }
            }
          }
        }
      ],
      id: "1",
      name: "Dynamic Select Options - Foundation Levels by key"
    }
  ];
  return { content: mockForm };
}

// Question type
function dynamicSelectQuestionType(): Story["args"] {
  const mockForm: Section["content"] = [
    {
      content: [
        {
          text: "Form",
          type: "select",
          id: "form",
          identifier: "form",
          properties: {
            dynamicOptions: {
              tag: DynamicOptionTags.QUESTION_TYPES
            }
          }
        }
      ],
      id: "1",
      name: "Dynamic Select Options - Question types"
    }
  ];
  return { content: mockForm };
}

// More future use case
// Will use our api client to fetch and process the options
// function apiDynamicSelect(): Story["args"] {
//   const mockForm: Section["content"] = [
//     {
//       content: [
//         {
//           text: "Question",
//           type: "select",
//           id: "question",
//           properties: {
//             dynamicOptions: {
//               tag: "api",
//               url: "/api/form-configuration/{{formConfigurationId}}/questions",
//               body: {
//                 formConfigurationId: "23409i12n"
//               },
//               headers: {
//                 Authorization: ""
//               }
//             }
//           }
//         }
//       ],
//       id: "1",
//       name: "Dynamic Select Options - from API"
//     }
//   ];
//   return { content: mockForm };
// }
