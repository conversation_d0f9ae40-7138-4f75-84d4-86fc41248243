import React, { ReactNode, useCallback, useEffect, useState } from "react";

import { CursorContext } from "./CursorContext";

export const CursorProvider = ({ children }: { children: ReactNode }) => {
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });
  const [isInputFocused, setIsInputFocused] = useState(false);

  const updatePosition = useCallback(
    (event: MouseEvent) => {
      // Skip cursor updates when an input is focused to prevent clearing typed values
      if (isInputFocused) {
        return;
      }
      setCursorPosition({ x: event.clientX, y: event.clientY });
    },
    [isInputFocused]
  );

  useEffect(() => {
    const handleFocus = (e: FocusEvent) => {
      if (
        e.target instanceof HTMLTextAreaElement
        //  ||
        // e.target instanceof HTMLInputElement
      ) {
        console.log("Input focused, skipping cursor update");
        setIsInputFocused(true);
      }
    };

    const handleBlur = (e: FocusEvent) => {
      if (
        e.target instanceof HTMLTextAreaElement
        //  ||
        // e.target instanceof HTMLInputElement
      ) {
        setIsInputFocused(false);
      }
    };

    document.addEventListener("focusin", handleFocus);
    document.addEventListener("focusout", handleBlur);
    document.addEventListener("mousemove", updatePosition);

    return () => {
      document.removeEventListener("focusin", handleFocus);
      document.removeEventListener("focusout", handleBlur);
      document.removeEventListener("mousemove", updatePosition);
    };
  }, [updatePosition]);

  const contextValue = React.useMemo(
    () => ({ cursorPosition }),
    [cursorPosition]
  );

  return (
    <CursorContext.Provider value={contextValue}>
      {children}
    </CursorContext.Provider>
  );
};
