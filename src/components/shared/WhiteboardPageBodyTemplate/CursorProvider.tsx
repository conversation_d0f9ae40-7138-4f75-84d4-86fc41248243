import React, {
  ReactNode,
  useCallback,
  useEffect,
  useRef,
  useState
} from "react";

import { CursorContext } from "./CursorContext";

export const CursorProvider = ({ children }: { children: ReactNode }) => {
  const cursorRef = useRef({ x: 0, y: 0 });
  const [isInputFocused, setIsInputFocused] = useState(false);

  const getCurrentPosition = useCallback(() => cursorRef.current, []);

  const updatePosition = useCallback((event: MouseEvent) => {
    // Skip cursor updates when an input is focused to prevent clearing typed values
    // if (isInputFocused) {
    //   return;
    // }
    // Update ref instead of state (no re-render)
    cursorRef.current = { x: event.clientX, y: event.clientY };
  }, []);

  useEffect(() => {
    // const handleFocus = (e: FocusEvent) => {
    //   if (
    //     e.target instanceof HTMLElement &&
    //     (e.target.classList.contains("text-field__input") ||
    //       e.target.classList.contains("text-area-field__input"))
    //   ) {
    //     setIsInputFocused(true);
    //   }
    // };

    // const handleBlur = (e: FocusEvent) => {
    //   if (
    //     e.target instanceof HTMLElement &&
    //     (e.target.classList.contains("text-field__input") ||
    //       e.target.classList.contains("text-area-field__input"))
    //   ) {
    //     setIsInputFocused(false);
    //   }
    // };

    // document.addEventListener("focusin", handleFocus);
    // document.addEventListener("focusout", handleBlur);
    document.addEventListener("mousemove", updatePosition);

    return () => {
      // document.removeEventListener("focusin", handleFocus);
      // document.removeEventListener("focusout", handleBlur);
      document.removeEventListener("mousemove", updatePosition);
    };
  }, [updatePosition]);

  const contextValue = React.useMemo(
    () => ({ getCurrentPosition }),
    [getCurrentPosition]
  );

  return (
    <CursorContext.Provider value={contextValue}>
      {children}
    </CursorContext.Provider>
  );
};
